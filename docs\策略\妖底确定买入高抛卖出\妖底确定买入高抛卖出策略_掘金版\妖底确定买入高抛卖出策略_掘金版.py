# coding=utf-8
"""
妖底确定买入高抛卖出量化策略 - 掘金平台版本
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前5分钟(14:55)使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位，已持有股票可继续加仓
3. 收盘前5分钟检测持仓股票是否触发卖出信号
4. 如有卖出信号则全仓卖出

适配掘金平台特点：
- 使用掘金API获取数据和执行交易
- 保持原有策略逻辑不变
- 支持回测和实盘交易
"""

from __future__ import print_function, absolute_import, unicode_literals
from gm.api import *
import pandas as pd
import numpy as np
import datetime
import random

# 尝试导入talib，如果不可用则使用备用方案
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("talib不可用，将使用备用MACD计算方法")


def init(context):
    """
    策略初始化函数
    """
    # 策略配置
    context.position_ratio = 0.1  # 单次买入仓位比例（十分之一）
    context.trade_time = "14:55"  # 交易时间
    context.commission_rate = 0.0003  # 手续费率
    context.stamp_tax = 0.001  # 印花税
    context.min_commission = 5  # 最小手续费
    
    # 每日定时任务 - 收盘前3分钟执行策略
    schedule(schedule_func=trade_strategy, date_rule='1d', time_rule='14:57:00')
    
    print('妖底确定买入高抛卖出策略初始化完成')
    print(f'策略参数: 单次买入仓位比例={context.position_ratio}')


def trade_strategy(context):
    """
    主交易策略函数
    """
    try:
        print(f"\n=== {context.now} 策略执行开始 ===")
        
        # 1. 优先检查卖出信号
        check_sell_signals(context)
        
        # 2. 检查买入信号
        check_buy_signals(context)
        
        # 3. 记录当前持仓状态
        log_positions(context)
        
        print("=== 策略执行完成 ===\n")
        
    except Exception as e:
        print(f"策略执行出错: {e}")



def sma_tdx(series, period, weight=1):
    """
    通达信SMA函数的精确实现
    SMA(X, N, M) = (M*X + (N-M)*SMA')/N
    其中SMA'是前一个SMA值
    """
    return series.ewm(alpha=weight/period, adjust=False).mean()


def filter_tdx(condition, period):
    """
    通达信FILTER函数的精确实现
    FILTER(X, N): 当X条件成立时，将其后N周期内的数据置为0
    这里用rolling sum > 0来近似，但可能存在差异
    """
    return condition.rolling(period).sum() > 0


def cross_tdx(series1, value):
    """
    通达信CROSS函数的精确实现
    CROSS(A, B): A向上穿越B时返回1
    """
    return (series1 > value) & (series1.shift(1) <= value)


def calculate_macd(close_prices, fast=12, slow=26, signal=9):
    """
    计算MACD指标，使用ewm以确保一致性
    """
    dif = close_prices.ewm(span=fast, adjust=False).mean() - close_prices.ewm(span=slow, adjust=False).mean()
    dea = dif.ewm(span=signal, adjust=False).mean()
    macd = (dif - dea) * 2
    return macd


def calculate_yaodi_signal(context, stock):
    """
    计算妖底确定买入信号 - 与通达信版逻辑完全对齐
    """
    try:
        # 时间条件检查 - 与通达信对齐
        current_date = context.now
        start_limit = datetime.datetime(2012, 8, 16)  # 1220816
        end_limit = datetime.datetime(2025, 8, 1)     # 1250801

        # 如果当前时间不在有效范围内，直接返回False
        if current_date < start_limit or current_date > end_limit:
            return False

        # 获取足够的历史数据
        end_date = context.now.strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=200)).strftime('%Y-%m-%d')
        
        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date, 
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True, 
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)
        
        if df is None or len(df) < 100:
            return False
            
        # 重命名列以匹配公式
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})
        
        C = df['C']
        H = df['H']
        L = df['L']
        
        # --- 核心指标计算 (与聚宽版对齐) ---

        # C1指标
        MA30 = C.rolling(30).mean()
        MA60 = C.rolling(60).mean()
        C1 = ((MA30 - L) / MA60) * 200

        # M2指标 - 使用通达信SMA精确实现
        price_changes = C.diff()
        abs_changes = price_changes.abs()
        positive_changes = price_changes.where(price_changes > 0, 0)
        M2 = sma_tdx(positive_changes, 7, 1) / sma_tdx(abs_changes, 7, 1) * 100

        # G1条件 - 使用通达信FILTER精确实现
        g1_base = (M2.shift(1) < 20) & (M2 > M2.shift(1))
        G1 = filter_tdx(g1_base, 5)

        # TU条件：超跌状态
        MA40 = C.rolling(40).mean()
        TU = C / MA40 < 0.74

        # TDJ条件：振幅大于5%
        TDJ = (H - L) / C.shift(1) > 0.05

        # YUL条件: 5日内TDJ次数>1
        YUL = TDJ.rolling(5).sum() > 1

        # QD启动条件
        QD = TU & TDJ & YUL

        # NTDF相关计算
        EMA5 = C.ewm(span=5, adjust=False).mean()
        SMMA = EMA5.ewm(span=5, adjust=False).mean()
        IM = EMA5.diff()
        TSMMA = SMMA.diff()
        DIVMA = (EMA5 - SMMA).abs()
        ET = (IM + TSMMA) / 2
        TDF = DIVMA * (ET ** 3)
        NTDF = TDF / TDF.abs().rolling(15).max()

        # QR确定条件 - 使用通达信CROSS精确实现
        QR = cross_tdx(NTDF, -0.9)

        # MACD 计算
        MACD = calculate_macd(C)

        # XG选股条件 - 使用通达信FILTER精确实现
        xg_base = QD.shift(1) & (QR | (C > C.shift(1))) & (MACD > -1.5)
        XG = filter_tdx(xg_base, 10)

        # BD波段条件 - 使用通达信FILTER精确实现
        bd_base = ((G1 & (C1 > 20)) | (C > C.shift(1))) & QD.shift(1)
        BD = filter_tdx(bd_base, 10)

        # 最终妖底确定条件: COUNT(XG,13)>=1 AND BD
        final_cond1 = XG.rolling(13).sum() >= 1
        final_cond2 = BD

        # --- 信号判断 ---
        if len(final_cond1) == 0 or len(final_cond2) == 0:
            return False

        last_signal1 = final_cond1.iloc[-1] if not pd.isna(final_cond1.iloc[-1]) else False
        last_signal2 = final_cond2.iloc[-1] if not pd.isna(final_cond2.iloc[-1]) else False

        yaodi_signal = bool(last_signal1 and last_signal2)
        
        if yaodi_signal:
            # 获取股票名称以便打印
            try:
                inst = get_instrumentinfos(symbols=[stock])
                stock_name = inst[0]['sec_name'] if inst else '未知名称'
                print(f"发现买入信号: {stock} ({stock_name})")
            except Exception:
                print(f"发现买入信号: {stock}")

        return yaodi_signal
        
    except Exception as e:
        print(f"计算妖底确定信号出错 {stock}: {e}")
        return False


def calculate_gaopao_sell_signal(context, stock):
    """
    计算高抛低吸卖出信号 - 与聚宽版逻辑对齐
    """
    try:
        # 获取历史数据
        end_date = context.now.strftime('%Y-%m-%d')
        start_date = (context.now - datetime.timedelta(days=100)).strftime('%Y-%m-%d')
        
        df = history(symbol=stock, frequency='1d', start_time=start_date, end_time=end_date, 
                    fields='symbol,eob,open,close,high,low,volume', skip_suspended=True, 
                    fill_missing='Last', adjust=ADJUST_PREV, df=True)
        
        if df is None or len(df) < 50:
            return False
            
        # 重命名列
        df = df.rename(columns={'close': 'C', 'high': 'H', 'low': 'L'})
        
        C = df['C']
        H = df['H']
        L = df['L']
        
        # 参数设置
        N1 = 21
        N2 = 8
        
        # 计算加权价格
        VAR8 = (2 * C + H + L) / 4
        
        # 动态区间
        VAR9 = L.rolling(N1).min()
        VAR10 = H.rolling(N2).max()
        
        # 避免除零错误
        denominator = VAR10 - VAR9
        denominator = denominator.where(denominator != 0, 1)
        
        # MJ指标（对应秘籍指标），使用ewm(span=9)平滑
        raw_mj = (VAR8 - VAR9) / denominator * 100
        MJ = raw_mj.ewm(span=9).mean()
        
        # 卖出信号：MJ从上方跌破80
        sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)
        
        if len(sell_signal) > 0 and sell_signal.iloc[-1]:
            print(f"发现卖出信号: {stock}")
            return True
        else:
            return False
        
    except Exception as e:
        print(f"计算高抛低吸卖出信号出错 {stock}: {e}")
        return False


def get_stock_pool(context):
    """
    获取股票池，排除不符合条件的股票
    """
    try:
        # 获取所有A股股票
        stocks = get_instruments(exchanges=['SHSE', 'SZSE'], sec_types=[SEC_TYPE_STOCK])
        
        stock_list = []
        for stock in stocks:
            symbol = stock['symbol']
            
            # 排除北证股票（代码以4或8开头）
            if symbol.startswith('4') or symbol.startswith('8'):
                continue
                
            # 排除ST股票
            if 'ST' in stock.get('sec_name', ''):
                continue
                
            stock_list.append(symbol)
        
        return stock_list
        
    except Exception as e:
        print(f"获取股票池出错: {e}")
        return []


def check_buy_signals(context):
    """
    检查买入信号
    """
    try:
        # 获取股票池
        stock_pool = get_stock_pool(context)
        
        if not stock_pool:
            print("股票池为空，跳过买入检查")
            return
        
        # 获取当前账户信息
        account = context.account()
        available_cash = account.cash['available']  # 正确的可用资金
        
        if available_cash < 10000:  # 可用资金少于1万元时不买入
            print(f"可用资金不足: {available_cash:.2f}")
            return
        
        # 计算单次买入金额
        buy_amount = available_cash * context.position_ratio
        
        print(f"开始全市场扫描买入信号，股票池大小: {len(stock_pool)}...")
        
        buy_signals = []
        # 遍历整个股票池来寻找买入信号
        for i, stock in enumerate(stock_pool):
            # 打印进度
            if (i + 1) % 100 == 0:
                print(f"  已扫描 {i + 1}/{len(stock_pool)}...")

            try:
                if calculate_yaodi_signal(context, stock):
                    buy_signals.append(stock)
                    # 注意：这里不再提前停止
                        
            except Exception as e:
                print(f"检查股票 {stock} 买入信号出错: {e}")
                continue
        
        print(f"全市场扫描完成，共找到 {len(buy_signals)} 个买入信号。")

        # 一次性获取所有待买入股票的名称，提高效率
        buy_name_map = {}
        if buy_signals:
            try:
                buy_instruments = get_instrumentinfos(symbols=buy_signals)
                buy_name_map = {inst['symbol']: inst['sec_name'] for inst in buy_instruments}
            except Exception as e:
                print(f"获取待买入股票名称时出错: {e}")

        # 执行买入 - 买入所有扫描到的信号
        for stock in buy_signals:
            try:
                # 获取当前价格
                current_data = current(symbols=[stock])
                # 使用我们通过调试发现的正确键名 'price'
                if not current_data or current_data[0]['price'] <= 0:
                    print(f"无法获取 {stock} 当前价格或价格无效（可能停牌），跳过买入")
                    continue
                
                current_price = current_data[0]['price']
                shares = int(buy_amount / current_price / 100) * 100  # 按手买入
                
                if shares >= 100:
                    order_target_value(symbol=stock, 
                                       value=shares * current_price, 
                                       position_side=PositionSide_Long, 
                                       order_type=OrderType_Market)
                    
                    stock_name = buy_name_map.get(stock, '')
                    print(f"买入 {stock} ({stock_name}): {shares}股, 价格: {current_price:.2f}")
                else:
                    print(f"{stock} 计算股数不足100股，跳过买入")
                    
            except Exception as e:
                print(f"买入 {stock} 出错: {e}")
                
    except Exception as e:
        print(f"检查买入信号出错: {e}")


def check_sell_signals(context):
    """
    检查卖出信号
    """
    try:
        # 获取当前持仓
        positions = context.account().positions()
        
        if not positions:
            print("当前无持仓，跳过卖出检查")
            return
        
        print(f"开始检查卖出信号，持仓股票数: {len(positions)}")
        
        for position in positions:
            stock = position['symbol']
            
            try:
                if calculate_gaopao_sell_signal(context, stock):
                    # 全仓卖出
                    order_target_percent(symbol=stock, percent=0)
                    print(f"卖出 {stock}: 全仓卖出")
                    
            except Exception as e:
                print(f"检查股票 {stock} 卖出信号出错: {e}")
                
    except Exception as e:
        print(f"检查卖出信号出错: {e}")


def log_positions(context):
    """
    记录当前持仓状态
    """
    try:
        account = context.account()
        positions = account.positions()
        
        print(f"\n当前账户状态:")
        print(f"总资产: {account.cash['nav']:.2f}")
        print(f"可用资金: {account.cash['available']:.2f}")
        print(f"浮动盈亏: {account.cash['fpnl']:.2f}")
        print(f"持仓数量: {len(positions)}")
        
        if not positions:
            return

        print("持仓详情:")
        
        # 一次性获取所有持仓股票的附加信息
        symbols = [p.symbol for p in positions]
        instruments = get_instrumentinfos(symbols=symbols)
        daily_data = current(symbols=symbols)

        # 创建方便查询的字典
        name_map = {inst['symbol']: inst['sec_name'] for inst in instruments}
        daily_map = {bar['symbol']: bar for bar in daily_data}

        for position in positions:
            symbol = position.symbol
            stock_name = name_map.get(symbol, '未知名称')
            
            # 获取浮动盈亏
            floating_pnl = position.fpnl

            # 计算当日涨跌幅
            daily_change_pct = 0.0
            daily_info = daily_map.get(symbol)
            if daily_info and daily_info.get('price', 0) > 0:
                price = daily_info['price']
                # 在回测模式下，current()函数不返回pre_close字段，跳过涨跌幅计算
                pre_close = daily_info.get('pre_close', 0)
                if pre_close > 0:
                    daily_change_pct = (price - pre_close) / pre_close * 100

            print(f"  代码: {symbol} ({stock_name}), "
                  f"数量: {position.volume}股, "
                  f"浮动盈动: {floating_pnl:.2f}, "
                  f"当日涨跌幅: {daily_change_pct:.2f}%")
                
    except Exception as e:
        print(f"记录持仓状态出错: {e}")


if __name__ == '__main__':
    """
    策略运行入口
    """
    run(strategy_id='yaodi_gaopao_strategy',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='{{token}}',
        backtest_start_time='2025-01-01 08:00:00',
        backtest_end_time='2025-07-01 16:00:00',
        backtest_initial_cash=1000000,
        backtest_commission_ratio=0.0003,
        backtest_slippage_ratio=0.0001)
